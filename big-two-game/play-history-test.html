<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出牌歷史測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-area {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 200px;
            max-height: 400px;
        }
        .test-area h3 {
            text-align: center;
            margin-bottom: 15px;
            color: #ffd700;
        }
        .played-cards {
            max-height: 300px;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background-color: rgba(0, 0, 0, 0.2);
        }
        .play-record {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.05);
            border-left: 3px solid rgba(255, 255, 255, 0.3);
        }
        .play-record.latest-play {
            background-color: rgba(255, 215, 0, 0.1);
            border-left-color: #ffd700;
            box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
        }
        .player-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .player-name {
            font-weight: bold;
            color: #b3d9ff;
        }
        .hand-type {
            color: #ffd700;
            font-size: 12px;
            background-color: rgba(255, 215, 0, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
        }
        .cards-container {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        .card {
            width: 40px;
            height: 60px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            font-size: 12px;
            background: linear-gradient(145deg, #ffffff, #e6e6e6);
            box-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            border: 1px solid #ccc;
        }
        .card.red {
            color: #d32f2f;
        }
        .card.black {
            color: #333;
        }
        .card-rank {
            font-size: 14px;
            font-weight: bold;
        }
        .card-suit {
            font-size: 16px;
        }
        button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            background: #007bff;
            color: white;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .last-play-info {
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
            color: #b3d9ff;
        }
        h1 { text-align: center; margin-bottom: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🃏 出牌歷史顯示測試</h1>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="addTestPlay()">添加測試出牌</button>
            <button onclick="clearHistory()">清空歷史</button>
            <button onclick="addMultiplePlays()">添加多個出牌</button>
        </div>
        
        <div class="test-area">
            <h3>出牌歷史</h3>
            <div id="playedCards" class="played-cards"></div>
            <div class="last-play-info" id="lastPlayInfo"></div>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
            <h3>功能說明</h3>
            <ul>
                <li>✅ 顯示所有玩家的出牌歷史</li>
                <li>✅ 最新出牌會有金色高亮效果</li>
                <li>✅ 每個出牌記錄顯示玩家名稱和牌型</li>
                <li>✅ 自動滾動到最新出牌</li>
                <li>✅ 支持滾動查看歷史記錄</li>
            </ul>
        </div>
    </div>
    
    <script>
        const SUITS = ['♣', '♦', '♥', '♠'];
        const RANKS = {
            '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
            'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15
        };
        
        const HandType = {
            SINGLE: 'single',
            PAIR: 'pair',
            THREE: 'three',
            STRAIGHT: 'straight'
        };
        
        class Card {
            constructor(suit, rank) {
                this.suit = suit;
                this.rank = rank;
                this.value = this.calculateValue();
            }
            
            calculateValue() {
                const rankValue = RANKS[this.rank];
                const suitValue = SUITS.indexOf(this.suit);
                return rankValue * 4 + suitValue;
            }
            
            isRed() {
                return this.suit === '♥' || this.suit === '♦';
            }
            
            toString() {
                return `${this.suit}${this.rank}`;
            }
        }
        
        let playHistory = [];
        let playedCardsDiv = null;
        let lastPlayInfo = null;
        
        document.addEventListener('DOMContentLoaded', function() {
            playedCardsDiv = document.getElementById('playedCards');
            lastPlayInfo = document.getElementById('lastPlayInfo');
            updatePlayedCards();
        });
        
        function getHandTypeName(type) {
            const names = {
                [HandType.SINGLE]: '單張',
                [HandType.PAIR]: '對子',
                [HandType.THREE]: '三條',
                [HandType.STRAIGHT]: '順子'
            };
            return names[type] || '未知';
        }
        
        function updatePlayedCards() {
            if (playHistory.length === 0) {
                playedCardsDiv.innerHTML = '<p style="color: #ccc;">尚未出牌</p>';
                lastPlayInfo.textContent = '';
                return;
            }

            playedCardsDiv.innerHTML = '';
            
            // 显示所有出牌历史，最新的在最下面
            playHistory.forEach((playRecord, index) => {
                const playDiv = document.createElement('div');
                playDiv.className = 'play-record';
                
                // 判断是否是最新的出牌
                const isLatest = index === playHistory.length - 1;
                if (isLatest) {
                    playDiv.classList.add('latest-play');
                }
                
                // 创建玩家信息
                const playerInfo = document.createElement('div');
                playerInfo.className = 'player-info';
                playerInfo.innerHTML = `
                    <span class="player-name">${playRecord.playerName}</span>
                    <span class="hand-type">${getHandTypeName(playRecord.hand.type)}</span>
                `;
                playDiv.appendChild(playerInfo);
                
                // 创建牌组显示
                const cardsContainer = document.createElement('div');
                cardsContainer.className = 'cards-container';
                
                playRecord.hand.cards.forEach(card => {
                    const cardDiv = document.createElement('div');
                    cardDiv.className = `card ${card.isRed() ? 'red' : 'black'}`;
                    cardDiv.innerHTML = `
                        <span class="card-rank">${card.rank}</span>
                        <span class="card-suit">${card.suit}</span>
                    `;
                    cardsContainer.appendChild(cardDiv);
                });
                
                playDiv.appendChild(cardsContainer);
                playedCardsDiv.appendChild(playDiv);
            });
            
            // 更新最后出牌信息
            if (playHistory.length > 0) {
                const lastPlay = playHistory[playHistory.length - 1];
                lastPlayInfo.textContent = `最新出牌：${lastPlay.playerName} 出了 ${getHandTypeName(lastPlay.hand.type)}`;
            }
            
            // 自动滚动到最新出牌
            playedCardsDiv.scrollTop = playedCardsDiv.scrollHeight;
        }
        
        function addTestPlay() {
            const players = ['你', 'AI 玩家 1', 'AI 玩家 2', 'AI 玩家 3'];
            const playerName = players[playHistory.length % players.length];
            
            // 创建随机测试牌
            const testCards = [
                { cards: [new Card('♣', '3')], type: HandType.SINGLE },
                { cards: [new Card('♠', '4'), new Card('♥', '4')], type: HandType.PAIR },
                { cards: [new Card('♦', '5'), new Card('♣', '5'), new Card('♠', '5')], type: HandType.THREE },
                { cards: [new Card('♥', 'K')], type: HandType.SINGLE }
            ];
            
            const randomHand = testCards[Math.floor(Math.random() * testCards.length)];
            
            playHistory.push({
                playerIndex: playHistory.length % 4,
                playerName: playerName,
                hand: randomHand,
                timestamp: new Date()
            });
            
            updatePlayedCards();
        }
        
        function clearHistory() {
            playHistory = [];
            updatePlayedCards();
        }
        
        function addMultiplePlays() {
            const testPlays = [
                { playerName: '你', hand: { cards: [new Card('♣', '3')], type: HandType.SINGLE } },
                { playerName: 'AI 玩家 1', hand: { cards: [new Card('♠', '3')], type: HandType.SINGLE } },
                { playerName: 'AI 玩家 2', hand: { cards: [new Card('♥', '4'), new Card('♦', '4')], type: HandType.PAIR } },
                { playerName: 'AI 玩家 3', hand: { cards: [new Card('♠', '5'), new Card('♣', '5')], type: HandType.PAIR } },
                { playerName: '你', hand: { cards: [new Card('♥', '6'), new Card('♦', '6'), new Card('♠', '6')], type: HandType.THREE } }
            ];
            
            testPlays.forEach((play, index) => {
                playHistory.push({
                    playerIndex: index % 4,
                    playerName: play.playerName,
                    hand: play.hand,
                    timestamp: new Date()
                });
            });
            
            updatePlayedCards();
        }
    </script>
</body>
</html>
