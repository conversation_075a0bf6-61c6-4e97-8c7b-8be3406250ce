<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花色修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success { background: rgba(40, 167, 69, 0.2); border-color: #28a745; }
        .error { background: rgba(220, 53, 69, 0.2); border-color: #dc3545; }
        .info { background: rgba(23, 162, 184, 0.2); border-color: #17a2b8; }
        
        .card-display {
            display: flex;
            justify-content: space-around;
            align-items: center;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
        }
        .card {
            font-size: 32px;
            font-weight: bold;
            padding: 10px 15px;
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
            text-align: center;
        }
        .vs {
            font-size: 24px;
            font-weight: bold;
        }
        .arrow {
            font-size: 24px;
            color: #28a745;
        }
        h1 { text-align: center; margin-bottom: 30px; }
        h2 { color: #ffd700; }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ 花色顺序修复验证</h1>
        
        <div class="result info">
            <h2>🎯 正确的花色顺序</h2>
            <p><strong>♣ (梅花) < ♦ (方块) < ♥ (红心) < ♠ (黑桃)</strong></p>
        </div>
        
        <div id="verification"></div>
    </div>
    
    <script>
        // 使用正确的花色顺序
        const SUITS = ['♣', '♦', '♥', '♠'];  // 梅花 < 方块 < 红心 < 黑桃
        const RANKS = {
            '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
            'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15
        };
        
        class Card {
            constructor(suit, rank) {
                this.suit = suit;
                this.rank = rank;
                this.value = this.calculateValue();
            }
            
            calculateValue() {
                const rankValue = RANKS[this.rank];
                const suitValue = SUITS.indexOf(this.suit);
                return rankValue * 4 + suitValue;
            }
            
            toString() {
                return `${this.suit}${this.rank}`;
            }
        }
        
        function addResult(content, type = 'info') {
            const verification = document.getElementById('verification');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = content;
            verification.appendChild(div);
        }
        
        function runVerification() {
            // 测试用户报告的具体情况：红心3 vs 黑桃3
            const heart3 = new Card('♥', '3');  // AI出的红心3
            const spade3 = new Card('♠', '3');  // 玩家的黑桃3
            
            addResult(`
                <h2>🔍 用户报告的问题验证</h2>
                <div class="card-display">
                    <div class="card">${heart3.toString()}</div>
                    <div class="vs">VS</div>
                    <div class="card">${spade3.toString()}</div>
                </div>
                <p>AI出红心3，玩家有黑桃3</p>
            `, 'info');
            
            addResult(`
                <p><strong>牌值计算：</strong></p>
                <p>红心3: 3 × 4 + ${SUITS.indexOf('♥')} = ${heart3.value}</p>
                <p>黑桃3: 3 × 4 + ${SUITS.indexOf('♠')} = ${spade3.value}</p>
            `, 'info');
            
            const canPlay = spade3.value > heart3.value;
            
            if (canPlay) {
                addResult(`
                    <h2>✅ 修复成功！</h2>
                    <div class="card-display">
                        <div class="card">${spade3.toString()}</div>
                        <div class="arrow">→ 可以压过 →</div>
                        <div class="card">${heart3.toString()}</div>
                    </div>
                    <p>现在玩家的黑桃3可以成功压过AI的红心3了！</p>
                `, 'success');
            } else {
                addResult(`
                    <h2>❌ 仍有问题</h2>
                    <p>黑桃3仍然无法压过红心3</p>
                `, 'error');
            }
            
            // 显示完整的花色顺序验证
            addResult(`
                <h2>🎴 完整花色顺序验证</h2>
            `, 'info');
            
            const testCards = SUITS.map(suit => new Card(suit, '3'));
            let orderCorrect = true;
            
            for (let i = 0; i < testCards.length; i++) {
                const card = testCards[i];
                const suitIndex = SUITS.indexOf(card.suit);
                addResult(`
                    <p>${card.toString()}: 花色索引=${suitIndex}, 总值=${card.value}</p>
                `, 'info');
                
                if (i > 0 && card.value <= testCards[i-1].value) {
                    orderCorrect = false;
                }
            }
            
            if (orderCorrect) {
                addResult(`
                    <h2>✅ 花色顺序完全正确</h2>
                    <p>所有花色按正确顺序排列：♣ < ♦ < ♥ < ♠</p>
                `, 'success');
            } else {
                addResult(`
                    <h2>❌ 花色顺序仍有问题</h2>
                `, 'error');
            }
        }
        
        // 页面加载时自动运行验证
        document.addEventListener('DOMContentLoaded', runVerification);
    </script>
</body>
</html>
