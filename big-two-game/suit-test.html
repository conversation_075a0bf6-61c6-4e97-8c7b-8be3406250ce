<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花色大小测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success { background: rgba(40, 167, 69, 0.2); border-color: #28a745; }
        .error { background: rgba(220, 53, 69, 0.2); border-color: #dc3545; }
        .info { background: rgba(23, 162, 184, 0.2); border-color: #17a2b8; }
        .warning { background: rgba(255, 193, 7, 0.2); border-color: #ffc107; }
        
        button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            background: #007bff;
            color: white;
        }
        button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .card-comparison {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255,255,255,0.1);
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .card {
            font-size: 24px;
            font-weight: bold;
            padding: 5px 10px;
            background: rgba(255,255,255,0.2);
            border-radius: 5px;
        }
        .vs {
            font-size: 18px;
            font-weight: bold;
        }
        .result {
            font-size: 16px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🃏 大老二花色大小测试</h1>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="testSuitOrder()">测试花色顺序</button>
            <button onclick="testSpecificCase()">测试红心3 vs 黑桃3</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="results"></div>
    </div>
    
    <script>
        // 使用修复后的花色顺序
        const SUITS = ['♣', '♦', '♥', '♠'];  // 梅花 < 方块 < 红心 < 黑桃
        const RANKS = {
            '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
            'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15
        };
        
        class Card {
            constructor(suit, rank) {
                this.suit = suit;
                this.rank = rank;
                this.value = this.calculateValue();
            }
            
            calculateValue() {
                const rankValue = RANKS[this.rank];
                const suitValue = SUITS.indexOf(this.suit);
                return rankValue * 4 + suitValue;
            }
            
            toString() {
                return `${this.suit}${this.rank}`;
            }
        }
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function compareCards(card1, card2) {
            return card1.value > card2.value;
        }
        
        function testSuitOrder() {
            addResult('<h3>🎯 花色顺序测试</h3>', 'info');
            addResult('正确的花色大小顺序应该是：♣ (梅花) < ♦ (方块) < ♥ (红心) < ♠ (黑桃)', 'info');
            
            // 测试相同点数不同花色的牌
            const testRank = '3';
            const cards = SUITS.map(suit => new Card(suit, testRank));
            
            addResult(`<h4>测试 ${testRank} 的所有花色：</h4>`, 'info');
            
            cards.forEach((card, index) => {
                const suitIndex = SUITS.indexOf(card.suit);
                addResult(`${card.toString()}: 花色值=${suitIndex}, 总值=${card.value}`, 'info');
            });
            
            // 验证顺序
            let orderCorrect = true;
            for (let i = 0; i < cards.length - 1; i++) {
                if (cards[i].value >= cards[i + 1].value) {
                    orderCorrect = false;
                    break;
                }
            }
            
            if (orderCorrect) {
                addResult('✅ 花色顺序正确！', 'success');
            } else {
                addResult('❌ 花色顺序错误！', 'error');
            }
        }
        
        function testSpecificCase() {
            addResult('<h3>🎯 红心3 vs 黑桃3 测试</h3>', 'info');
            
            const heart3 = new Card('♥', '3');  // 红心3
            const spade3 = new Card('♠', '3');  // 黑桃3
            
            addResult(`<div class="card-comparison">
                <div class="card">${heart3.toString()}</div>
                <div class="vs">VS</div>
                <div class="card">${spade3.toString()}</div>
            </div>`, 'info');
            
            addResult(`红心3 值: ${heart3.value} (花色值: ${SUITS.indexOf(heart3.suit)})`, 'info');
            addResult(`黑桃3 值: ${spade3.value} (花色值: ${SUITS.indexOf(spade3.suit)})`, 'info');
            
            const spadeWins = compareCards(spade3, heart3);
            
            if (spadeWins) {
                addResult('✅ 结果正确：黑桃3 > 红心3', 'success');
                addResult('现在玩家可以用黑桃3压过AI的红心3了！', 'success');
            } else {
                addResult('❌ 结果错误：黑桃3应该大于红心3', 'error');
            }
            
            // 测试其他组合
            addResult('<h4>其他花色组合测试：</h4>', 'info');
            
            const testCases = [
                [new Card('♣', '3'), new Card('♦', '3'), '♣3 < ♦3'],
                [new Card('♦', '3'), new Card('♥', '3'), '♦3 < ♥3'],
                [new Card('♥', '3'), new Card('♠', '3'), '♥3 < ♠3'],
                [new Card('♣', '3'), new Card('♠', '3'), '♣3 < ♠3']
            ];
            
            testCases.forEach(([card1, card2, expected]) => {
                const result = compareCards(card2, card1);
                const status = result ? '✅' : '❌';
                addResult(`${status} ${expected}: ${result ? '正确' : '错误'}`, result ? 'success' : 'error');
            });
        }
        
        // 页面加载时显示说明
        document.addEventListener('DOMContentLoaded', function() {
            addResult('<h3>🎮 大老二花色大小规则</h3>', 'info');
            addResult('在大老二游戏中，当点数相同时，花色决定大小：', 'info');
            addResult('♣ (梅花) < ♦ (方块) < ♥ (红心) < ♠ (黑桃)', 'warning');
            addResult('所以黑桃3应该能压过红心3！', 'info');
        });
    </script>
</body>
</html>
