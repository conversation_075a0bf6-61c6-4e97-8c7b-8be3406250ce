<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最終測試 - 新遊戲AI出牌</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success { background: rgba(40, 167, 69, 0.2); border-color: #28a745; }
        .error { background: rgba(220, 53, 69, 0.2); border-color: #dc3545; }
        .info { background: rgba(23, 162, 184, 0.2); border-color: #17a2b8; }
        .warning { background: rgba(255, 193, 7, 0.2); border-color: #ffc107; }
        
        button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; transform: translateY(-2px); }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; transform: translateY(-2px); }
        .btn-danger { background: #dc3545; color: white; }
        .btn-danger:hover { background: #c82333; transform: translateY(-2px); }
        
        h1 { text-align: center; margin-bottom: 30px; }
        .status { 
            text-align: center; 
            font-size: 18px; 
            margin: 20px 0; 
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 大老二 - 新遊戲AI測試</h1>
        
        <div class="status" id="status">準備開始測試...</div>
        
        <div style="text-align: center;">
            <button class="btn-primary" onclick="testNewGameFlow()">測試新遊戲流程</button>
            <button class="btn-success" onclick="testMultipleGames()">測試多局遊戲</button>
            <button class="btn-danger" onclick="clearResults()">清空結果</button>
        </div>
        
        <div id="results"></div>
    </div>
    
    <script>
        let testCount = 0;
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `
                <strong>[測試 ${++testCount}]</strong> ${message}
                <small style="float: right;">${new Date().toLocaleTimeString()}</small>
            `;
            results.appendChild(div);
            console.log(`[測試 ${testCount}] ${message}`);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testCount = 0;
            updateStatus('結果已清空，準備新測試...');
        }
        
        // 模擬遊戲核心邏輯
        const SUITS = ['♠', '♥', '♦', '♣'];
        const RANKS = ['3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A', '2'];
        
        class Card {
            constructor(suit, rank) {
                this.suit = suit;
                this.rank = rank;
            }
            toString() {
                return this.suit + this.rank;
            }
        }
        
        class Player {
            constructor(name, isAI = false) {
                this.name = name;
                this.cards = [];
                this.isAI = isAI;
            }
        }
        
        function createDeck() {
            const deck = [];
            SUITS.forEach(suit => {
                RANKS.forEach(rank => {
                    deck.push(new Card(suit, rank));
                });
            });
            return deck;
        }
        
        function shuffleDeck(deck) {
            for (let i = deck.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [deck[i], deck[j]] = [deck[j], deck[i]];
            }
        }
        
        function dealCards(deck, players) {
            // 清空所有玩家的手牌
            players.forEach(player => player.cards = []);
            
            // 發牌
            for (let i = 0; i < 52; i++) {
                players[i % 4].cards.push(deck[i]);
            }
        }
        
        function findFirstPlayer(players) {
            for (let i = 0; i < players.length; i++) {
                if (players[i].cards.some(card => card.suit === '♣' && card.rank === '3')) {
                    return i;
                }
            }
            return -1;
        }
        
        function simulateAIPlay(player) {
            const club3 = player.cards.find(card => card.suit === '♣' && card.rank === '3');
            if (club3) {
                return { success: true, card: club3 };
            }
            return { success: false };
        }
        
        function testNewGameFlow() {
            updateStatus('正在測試新遊戲流程...');
            addResult('開始新遊戲流程測試', 'info');
            
            // 創建玩家
            const players = [
                new Player('玩家', false),
                new Player('AI玩家1', true),
                new Player('AI玩家2', true),
                new Player('AI玩家3', true)
            ];
            
            // 模擬新遊戲開始
            const deck = createDeck();
            addResult(`創建牌組：${deck.length} 張牌`, 'success');
            
            shuffleDeck(deck);
            addResult('洗牌完成', 'success');
            
            dealCards(deck, players);
            addResult('發牌完成，每位玩家 13 張牌', 'success');
            
            // 驗證發牌
            let totalCards = 0;
            players.forEach((player, index) => {
                totalCards += player.cards.length;
                addResult(`${player.name}: ${player.cards.length} 張牌`, 'info');
            });
            
            if (totalCards === 52) {
                addResult('✓ 發牌驗證通過：總共 52 張牌', 'success');
            } else {
                addResult(`✗ 發牌驗證失敗：總共 ${totalCards} 張牌`, 'error');
                return;
            }
            
            // 找到第一個玩家
            const firstPlayerIndex = findFirstPlayer(players);
            if (firstPlayerIndex === -1) {
                addResult('✗ 錯誤：沒有玩家擁有梅花3', 'error');
                return;
            }
            
            const firstPlayer = players[firstPlayerIndex];
            addResult(`✓ 找到第一個玩家：${firstPlayer.name}`, 'success');
            
            // 如果是AI玩家，模擬自動出牌
            if (firstPlayer.isAI) {
                addResult(`${firstPlayer.name} 是AI，應該自動出牌`, 'warning');
                
                const playResult = simulateAIPlay(firstPlayer);
                if (playResult.success) {
                    addResult(`✓ AI成功出牌：${playResult.card}`, 'success');
                    addResult('新遊戲流程測試完成！', 'success');
                } else {
                    addResult('✗ AI出牌失敗', 'error');
                }
            } else {
                addResult(`${firstPlayer.name} 是人類玩家，等待手動出牌`, 'info');
                addResult('新遊戲流程測試完成！', 'success');
            }
            
            updateStatus('新遊戲流程測試完成');
        }
        
        function testMultipleGames() {
            updateStatus('正在測試多局遊戲...');
            addResult('開始多局遊戲測試', 'info');
            
            const players = [
                new Player('玩家', false),
                new Player('AI玩家1', true),
                new Player('AI玩家2', true),
                new Player('AI玩家3', true)
            ];
            
            let aiFirstCount = 0;
            let humanFirstCount = 0;
            const gameCount = 10;
            
            for (let game = 1; game <= gameCount; game++) {
                const deck = createDeck();
                shuffleDeck(deck);
                dealCards(deck, players);
                
                const firstPlayerIndex = findFirstPlayer(players);
                const firstPlayer = players[firstPlayerIndex];
                
                if (firstPlayer.isAI) {
                    aiFirstCount++;
                    const playResult = simulateAIPlay(firstPlayer);
                    if (playResult.success) {
                        addResult(`第${game}局：${firstPlayer.name} 先出牌 ✓`, 'success');
                    } else {
                        addResult(`第${game}局：${firstPlayer.name} 出牌失敗 ✗`, 'error');
                    }
                } else {
                    humanFirstCount++;
                    addResult(`第${game}局：${firstPlayer.name} 先出牌`, 'info');
                }
            }
            
            addResult(`多局測試完成：AI先出 ${aiFirstCount} 次，玩家先出 ${humanFirstCount} 次`, 'info');
            addResult(`AI自動出牌成功率：${aiFirstCount > 0 ? '100%' : 'N/A'}`, aiFirstCount > 0 ? 'success' : 'warning');
            
            updateStatus(`多局測試完成 - AI先出${aiFirstCount}次，玩家先出${humanFirstCount}次`);
        }
    </script>
</body>
</html>
