* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft JhengHei', <PERSON>l, sans-serif;
    background-color: #0d7835;
    color: white;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-container {
    width: 90%;
    max-width: 1200px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 20px;
    padding: 20px;
}

h1 {
    text-align: center;
    margin-bottom: 20px;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.game-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}

.current-player {
    font-size: 18px;
    font-weight: bold;
}

.game-status {
    font-size: 16px;
    color: #ffeb3b;
}

.play-area {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    min-height: 200px;
    max-height: 400px;
}

.play-area h3 {
    text-align: center;
    margin-bottom: 15px;
    color: #ffd700;
}

.played-cards {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.2);
}

/* 出牌记录样式 */
.play-record {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.05);
    border-left: 3px solid rgba(255, 255, 255, 0.3);
}

.play-record.latest-play {
    background-color: rgba(255, 215, 0, 0.1);
    border-left-color: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.player-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.player-name {
    font-weight: bold;
    color: #b3d9ff;
}

.hand-type {
    color: #ffd700;
    font-size: 12px;
    background-color: rgba(255, 215, 0, 0.2);
    padding: 2px 6px;
    border-radius: 4px;
}

.cards-container {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.last-play-info {
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
    color: #b3d9ff;
}

.players-section {
    margin-bottom: 20px;
}

.computer-players {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
}

.player-hand {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
}

.player-hand.computer {
    min-width: 150px;
}

.player-hand h4 {
    margin-bottom: 10px;
    color: #ffd700;
}

.cards-count {
    font-size: 14px;
    color: #b3d9ff;
}

.ai-level {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: normal;
    margin-left: 5px;
}

.ai-level.easy {
    background-color: #4caf50;
    color: white;
}

.ai-level.medium {
    background-color: #ff9800;
    color: white;
}

.ai-level.hard {
    background-color: #f44336;
    color: white;
}

.player-cards {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 15px;
}

.card {
    width: 60px;
    height: 90px;
    background-color: white;
    border: 2px solid #333;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.card.selected {
    transform: translateY(-15px);
    border: 3px solid #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.card.red {
    color: #e74c3c;
}

.card.black {
    color: #2c3e50;
}

.card-rank {
    font-size: 20px;
    font-weight: bold;
}

.card-suit {
    font-size: 16px;
    position: absolute;
    bottom: 5px;
    right: 5px;
}

.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.btn {
    padding: 12px 30px;
    font-size: 16px;
    font-weight: bold;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.btn:hover:not(:disabled) {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-play {
    background-color: #4caf50;
    color: white;
}

.btn-play:hover:not(:disabled) {
    background-color: #45a049;
}

.btn-pass {
    background-color: #ff9800;
    color: white;
}

.btn-pass:hover:not(:disabled) {
    background-color: #e68900;
}

.btn-new {
    background-color: #2196f3;
    color: white;
}

.btn-new:hover {
    background-color: #0b7dda;
}

.message-area {
    text-align: center;
    padding: 10px;
    min-height: 30px;
    font-size: 16px;
    color: #ffeb3b;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}

@media (max-width: 768px) {
    .computer-players {
        flex-direction: column;
        gap: 10px;
    }
    
    .card {
        width: 50px;
        height: 75px;
        font-size: 20px;
    }
    
    .controls {
        flex-wrap: wrap;
    }
}

.ai-stats {
    margin-top: 20px;
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.ai-stats h4 {
    text-align: center;
    color: #ffd700;
    margin-bottom: 15px;
    font-size: 18px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.stat-label {
    font-size: 14px;
    color: #b3d9ff;
}

.stat-value {
    font-size: 16px;
    font-weight: bold;
    color: #ffd700;
}

/* 梅花3高亮效果 */
.club-3-highlight {
    position: relative;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 10px #ffd700;
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 20px #ffd700, 0 0 30px #ffd700;
        transform: scale(1.05);
    }
    100% {
        box-shadow: 0 0 10px #ffd700;
        transform: scale(1);
    }
}