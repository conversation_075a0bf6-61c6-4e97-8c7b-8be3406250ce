<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第一輪出牌測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success { background: rgba(40, 167, 69, 0.2); border-color: #28a745; }
        .error { background: rgba(220, 53, 69, 0.2); border-color: #dc3545; }
        .info { background: rgba(23, 162, 184, 0.2); border-color: #17a2b8; }
        .warning { background: rgba(255, 193, 7, 0.2); border-color: #ffc107; }
        
        button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            background: #007bff;
            color: white;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .card-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .card {
            font-size: 20px;
            font-weight: bold;
            padding: 5px 10px;
            background: rgba(255,255,255,0.2);
            border-radius: 5px;
        }
        .club3 {
            background: rgba(255, 215, 0, 0.3);
            border: 2px solid #ffd700;
        }
        h1 { text-align: center; margin-bottom: 30px; }
        h2 { color: #ffd700; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🃏 第一輪出牌規則測試</h1>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="testFirstRoundRules()">測試第一輪規則</button>
            <button onclick="testPairWithClub3()">測試梅花3對子</button>
            <button onclick="clearResults()">清空結果</button>
        </div>
        
        <div id="results"></div>
    </div>
    
    <script>
        const SUITS = ['♣', '♦', '♥', '♠'];
        const RANKS = {
            '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
            'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15
        };
        
        const HandType = {
            SINGLE: 'single',
            PAIR: 'pair',
            THREE: 'three'
        };
        
        class Card {
            constructor(suit, rank) {
                this.suit = suit;
                this.rank = rank;
                this.value = this.calculateValue();
            }
            
            calculateValue() {
                const rankValue = RANKS[this.rank];
                const suitValue = SUITS.indexOf(this.suit);
                return rankValue * 4 + suitValue;
            }
            
            toString() {
                return `${this.suit}${this.rank}`;
            }
        }
        
        class MockGame {
            constructor() {
                this.firstRound = true;
                this.lastPlay = null;
            }
            
            identifyHand(cards) {
                if (cards.length === 1) {
                    return {
                        type: HandType.SINGLE,
                        cards: cards,
                        value: cards[0].value
                    };
                } else if (cards.length === 2 && cards[0].rank === cards[1].rank) {
                    return {
                        type: HandType.PAIR,
                        cards: cards,
                        value: Math.max(cards[0].value, cards[1].value)
                    };
                } else if (cards.length === 3 && cards[0].rank === cards[1].rank && cards[1].rank === cards[2].rank) {
                    return {
                        type: HandType.THREE,
                        cards: cards,
                        value: Math.max(...cards.map(c => c.value))
                    };
                }
                return null;
            }
            
            isValidPlay(hand) {
                if (this.firstRound) {
                    return hand.cards.some(card => card.suit === '♣' && card.rank === '3');
                }
                return true;
            }
        }
        
        function addResult(content, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = content;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function createCardDisplay(cards) {
            return cards.map(card => {
                const isClub3 = card.suit === '♣' && card.rank === '3';
                return `<span class="card ${isClub3 ? 'club3' : ''}">${card.toString()}</span>`;
            }).join('');
        }
        
        function testFirstRoundRules() {
            addResult('<h2>🎯 第一輪出牌規則測試</h2>', 'info');
            
            const game = new MockGame();
            
            // 測試案例
            const testCases = [
                {
                    name: '單張梅花3',
                    cards: [new Card('♣', '3')],
                    shouldPass: true
                },
                {
                    name: '梅花3對子',
                    cards: [new Card('♣', '3'), new Card('♠', '3')],
                    shouldPass: true
                },
                {
                    name: '梅花3三條',
                    cards: [new Card('♣', '3'), new Card('♦', '3'), new Card('♥', '3')],
                    shouldPass: true
                },
                {
                    name: '不含梅花3的對子',
                    cards: [new Card('♠', '4'), new Card('♥', '4')],
                    shouldPass: false
                },
                {
                    name: '單張黑桃3（不含梅花3）',
                    cards: [new Card('♠', '3')],
                    shouldPass: false
                }
            ];
            
            testCases.forEach(testCase => {
                const hand = game.identifyHand(testCase.cards);
                if (hand) {
                    const isValid = game.isValidPlay(hand);
                    const result = isValid === testCase.shouldPass;
                    
                    addResult(`
                        <div class="card-group">
                            <strong>${testCase.name}:</strong>
                            ${createCardDisplay(testCase.cards)}
                            <span style="margin-left: 20px;">
                                ${result ? '✅' : '❌'} 
                                ${isValid ? '有效' : '無效'}
                                ${result ? '' : '（預期' + (testCase.shouldPass ? '有效' : '無效') + '）'}
                            </span>
                        </div>
                    `, result ? 'success' : 'error');
                } else {
                    addResult(`
                        <div class="card-group">
                            <strong>${testCase.name}:</strong>
                            ${createCardDisplay(testCase.cards)}
                            <span style="margin-left: 20px;">❌ 無法識別牌型</span>
                        </div>
                    `, 'error');
                }
            });
        }
        
        function testPairWithClub3() {
            addResult('<h2>🎯 梅花3對子專項測試</h2>', 'info');
            
            const game = new MockGame();
            
            // 測試所有可能的梅花3對子組合
            const otherSuits = ['♦', '♥', '♠'];
            
            otherSuits.forEach(suit => {
                const cards = [new Card('♣', '3'), new Card(suit, '3')];
                const hand = game.identifyHand(cards);
                
                if (hand && hand.type === HandType.PAIR) {
                    const isValid = game.isValidPlay(hand);
                    
                    addResult(`
                        <div class="card-group">
                            <strong>梅花3 + ${suit}3 對子:</strong>
                            ${createCardDisplay(cards)}
                            <span style="margin-left: 20px;">
                                ${isValid ? '✅ 有效' : '❌ 無效'}
                            </span>
                        </div>
                    `, isValid ? 'success' : 'error');
                } else {
                    addResult(`
                        <div class="card-group">
                            <strong>梅花3 + ${suit}3:</strong>
                            ${createCardDisplay(cards)}
                            <span style="margin-left: 20px;">❌ 無法識別為對子</span>
                        </div>
                    `, 'error');
                }
            });
            
            addResult(`
                <h3>📋 結論</h3>
                <p>根據大老二規則，第一輪可以出任何包含梅花3的牌型：</p>
                <ul>
                    <li>✅ 單張梅花3</li>
                    <li>✅ 包含梅花3的對子（如♣3♠3）</li>
                    <li>✅ 包含梅花3的三條（如♣3♦3♥3）</li>
                    <li>✅ 包含梅花3的其他牌型</li>
                </ul>
            `, 'info');
        }
        
        // 頁面載入時顯示說明
        document.addEventListener('DOMContentLoaded', function() {
            addResult(`
                <h2>📖 第一輪出牌規則說明</h2>
                <p>在大老二遊戲中，第一輪（擁有梅花3的玩家）可以出任何包含梅花3的牌型：</p>
                <ul>
                    <li><strong>單張：</strong>梅花3</li>
                    <li><strong>對子：</strong>梅花3 + 其他花色的3（如♣3♠3）</li>
                    <li><strong>三條：</strong>三張3，其中包含梅花3</li>
                    <li><strong>其他牌型：</strong>只要包含梅花3即可</li>
                </ul>
                <p>點擊下方按鈕開始測試！</p>
            `, 'info');
        });
    </script>
</body>
</html>
