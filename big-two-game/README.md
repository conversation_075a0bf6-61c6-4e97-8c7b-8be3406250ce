# 大老二遊戲 - 增強AI版本

這是一個具有智能AI玩家的大老二紙牌遊戲。

## 🎮 遊戲特色

### AI玩家系統
- **三種難度級別**：
  - 🟢 **簡單AI**：基礎策略，適合新手練習
  - 🟡 **中等AI**：平衡策略，考慮手牌和對手威脅
  - 🔴 **困難AI**：高級策略，複雜決策算法

### AI個性化
每個AI玩家都有獨特的個性特徵：
- **積極性**：影響出牌的主動程度
- **冒險性**：影響是否願意冒險出牌
- **耐心度**：影響Pass的傾向
- **虛張聲勢**：影響心理戰術

### 智能策略
- **動態思考時間**：根據AI難度和個性調整思考時間
- **情境感知**：AI會根據遊戲狀態調整策略
- **終局策略**：當玩家接近獲勝時，AI會更加積極
- **阻擋策略**：AI會嘗試阻擋其他玩家獲勝

### 遊戲統計
- 追蹤總遊戲局數
- 顯示玩家和AI的勝率
- 數據持久化保存

## 🎯 AI策略詳解

### 簡單AI (綠色)
- 隨機選擇有效牌組
- 30%機率選擇Pass
- 優先出最小的牌

### 中等AI (橙色)
- 評估手牌價值和對手威脅
- 根據個性調整Pass機率
- 考慮阻擋對手的策略
- 終局時更加積極

### 困難AI (紅色)
- 複雜的遊戲狀態分析
- 多因素決策算法
- 精確的牌型價值計算
- 高級終局策略

## 🚀 如何開始

1. 打開 `index.html` 文件
2. 點擊「新遊戲」開始
3. 選擇要出的牌，點擊「出牌」
4. 或者選擇「Pass」跳過這一輪
5. 觀察AI玩家的智能表現！

## 🎲 遊戲規則

### 基本規則
- **第一輪特殊規則**：擁有梅花3(♣3)的玩家必須先出牌
- 第一輪必須出包含梅花3的牌組（可以是單張梅花3或包含梅花3的組合）
- 第一輪不能Pass，必須出牌
- 後續出牌必須大過上一手牌
- 支援的牌型：單張、對子、三條、順子、葫蘆、鐵支、同花順
- 最先出完所有牌的玩家獲勝

### 花色大小順序
- **正確順序**：♣ (梅花) < ♦ (方块) < ♥ (红心) < ♠ (黑桃)
- **相同點數時**：花色決定大小，黑桃最大，梅花最小
- **範例**：♠3 > ♥3 > ♦3 > ♣3

### 梅花3先出牌功能
- 🎯 **自動識別**：遊戲開始時自動找到擁有梅花3的玩家
- 💡 **視覺提示**：如果是人類玩家擁有梅花3，會有明確的文字提示
- ✨ **高亮顯示**：梅花3會有金色邊框和脈冲動畫效果，方便識別
- 🤖 **AI智能**：AI玩家會自動選擇包含梅花3的最佳牌組出牌
- 🚫 **防止錯誤**：第一輪如果不包含梅花3會顯示錯誤提示

## 🔧 技術特色

- 純JavaScript實現，無需額外依賴
- 響應式設計，支援各種螢幕尺寸
- 本地存儲統計數據
- 流暢的動畫效果
- 智能AI決策系統

享受與智能AI對戰的樂趣吧！🎉
