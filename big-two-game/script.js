// 遊戲常數定義
// 花色大小順序：梅花 < 方块 < 红心 < 黑桃
const SUITS = ['♣', '♦', '♥', '♠'];
const RANKS = {
    '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
    'J': 11, 'Q': 12, 'K': 13, 'A': 14, '2': 15
};

const HandType = {
    SINGLE: 'single',
    PAIR: 'pair',
    THREE: 'three',
    STRAIGHT: 'straight',
    FULL_HOUSE: 'full_house',
    FOUR_OF_KIND: 'four_of_kind',
    STRAIGHT_FLUSH: 'straight_flush'
};

const GameState = {
    WAITING: 'waiting',
    PLAYING: 'playing',
    ENDED: 'ended'
};

// AI 難度級別
const AILevel = {
    EASY: 'easy',
    MEDIUM: 'medium',
    HARD: 'hard'
};

// 撲克牌類別
class Card {
    constructor(suit, rank) {
        this.suit = suit;
        this.rank = rank;
        this.value = this.calculateValue();
    }

    calculateValue() {
        const rankValue = RANKS[this.rank];
        const suitValue = SUITS.indexOf(this.suit);
        return rankValue * 4 + suitValue;
    }

    isRed() {
        return this.suit === '♥' || this.suit === '♦';
    }

    toString() {
        return `${this.rank}${this.suit}`;
    }
}

// 玩家類別
class Player {
    constructor(name, isAI = false, aiLevel = AILevel.MEDIUM) {
        this.name = name;
        this.cards = [];
        this.isAI = isAI;
        this.aiLevel = aiLevel;
        this.personality = this.generatePersonality();
    }

    generatePersonality() {
        return {
            aggressiveness: Math.random() * 0.5 + 0.25, // 0.25-0.75
            riskTaking: Math.random() * 0.6 + 0.2,      // 0.2-0.8
            patience: Math.random() * 0.4 + 0.3,        // 0.3-0.7
            bluffing: Math.random() * 0.3 + 0.1         // 0.1-0.4
        };
    }

    sortCards() {
        this.cards.sort((a, b) => a.value - b.value);
    }
}

// 主遊戲類別
class BigTwo {
    constructor() {
        this.players = [
            new Player('你'),
            new Player('AI 玩家 1', true, AILevel.EASY),
            new Player('AI 玩家 2', true, AILevel.MEDIUM),
            new Player('AI 玩家 3', true, AILevel.HARD)
        ];

        this.deck = [];
        this.currentPlayerIndex = 0;
        this.gameState = GameState.WAITING;
        this.selectedCards = [];
        this.lastPlay = null;
        this.lastPlayerIndex = -1;
        this.passCount = 0;
        this.firstRound = true;

        // 統計數據
        this.gameStats = {
            totalGames: 0,
            playerWins: 0,
            aiWins: 0
        };

        this.initializeDOM();
        this.loadStats();
        this.startNewGame();
    }

    initializeDOM() {
        this.playerCardsDiv = document.getElementById('playerCards');
        this.playedCardsDiv = document.getElementById('playedCards');
        this.currentPlayerSpan = document.getElementById('currentPlayer');
        this.gameStatus = document.getElementById('gameStatus');
        this.lastPlayInfo = document.getElementById('lastPlayInfo');
        this.messageArea = document.getElementById('messageArea');
        this.playButton = document.getElementById('playButton');
        this.passButton = document.getElementById('passButton');
        this.newGameButton = document.getElementById('newGameButton');

        // 綁定事件
        this.playButton?.addEventListener('click', () => this.playCards());
        this.passButton?.addEventListener('click', () => this.pass());
        this.newGameButton?.addEventListener('click', () => this.startNewGame());
    }

    startNewGame() {
        // 重置遊戲狀態
        this.gameState = GameState.PLAYING;
        this.firstRound = true;
        this.lastPlay = null;
        this.lastPlayerIndex = -1;
        this.passCount = 0;
        this.selectedCards = [];
        this.currentPlayerIndex = 0;

        // 重置玩家狀態
        this.players.forEach(player => {
            player.cards = [];
        });

        // 創建新牌組並發牌
        this.createDeck();
        this.shuffleDeck();
        this.dealCards();
        this.findFirstPlayer();
        this.updateUI();

        console.log(`新遊戲開始，第一個玩家：${this.players[this.currentPlayerIndex].name}`);

        // 如果AI玩家擁有梅花3，觸發自動出牌
        if (this.currentPlayerIndex !== 0 && this.gameState === GameState.PLAYING) {
            const player = this.players[this.currentPlayerIndex];
            const thinkingTime = this.calculateThinkingTime(player);

            console.log(`AI玩家 ${player.name} 將在 ${thinkingTime}ms 後自動出牌`);
            setTimeout(() => {
                if (this.gameState === GameState.PLAYING && this.firstRound) {
                    this.computerPlay();
                }
            }, thinkingTime);
        }
    }

    createDeck() {
        this.deck = [];
        for (const suit of SUITS) {
            for (const rank of Object.keys(RANKS)) {
                this.deck.push(new Card(suit, rank));
            }
        }
    }

    shuffleDeck() {
        for (let i = this.deck.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.deck[i], this.deck[j]] = [this.deck[j], this.deck[i]];
        }
    }

    dealCards() {
        // 清空所有玩家的手牌
        this.players.forEach(player => player.cards = []);

        // 發牌
        for (let i = 0; i < 52; i++) {
            const playerIndex = i % 4;
            this.players[playerIndex].cards.push(this.deck[i]);
        }

        // 排序手牌
        this.players.forEach(player => player.sortCards());
    }

    findFirstPlayer() {
        // 找到有梅花3的玩家
        for (let i = 0; i < this.players.length; i++) {
            if (this.players[i].cards.some(card => card.suit === '♣' && card.rank === '3')) {
                this.currentPlayerIndex = i;
                if (i === 0) {
                    this.showMessage(`你擁有梅花3(♣3)，必須先出牌！可以出單張梅花3、包含梅花3的對子、三條等任何包含梅花3的牌組。`);
                } else {
                    this.showMessage(`${this.players[i].name} 擁有梅花3(♣3)，先出牌！`);
                }
                break;
            }
        }
        this.updateCurrentPlayer();
    }

    updateCurrentPlayer() {
        const currentPlayer = this.players[this.currentPlayerIndex];
        this.currentPlayerSpan.textContent = currentPlayer.name;
    }

    updateUI() {
        this.updatePlayerCards();
        this.updateGameInfo();
        this.updateCurrentPlayer();
        this.updateControls();
        this.updatePlayedCards();
        this.updatePlayerCounts();
    }

    updatePlayerCards() {
        const player = this.players[0];
        this.playerCardsDiv.innerHTML = '';

        player.cards.forEach((card, index) => {
            const cardElement = this.createCardElement(card, index);
            this.playerCardsDiv.appendChild(cardElement);
        });
    }

    createCardElement(card, index) {
        const cardDiv = document.createElement('div');
        cardDiv.className = `card ${card.isRed() ? 'red' : 'black'}`;
        cardDiv.dataset.index = index;
        
        const rankSpan = document.createElement('span');
        rankSpan.className = 'card-rank';
        rankSpan.textContent = card.rank;
        
        const suitSpan = document.createElement('span');
        suitSpan.className = 'card-suit';
        suitSpan.textContent = card.suit;
        
        cardDiv.appendChild(rankSpan);
        cardDiv.appendChild(suitSpan);
        
        cardDiv.addEventListener('click', () => this.toggleCardSelection(index));

        if (this.selectedCards.includes(index)) {
            cardDiv.classList.add('selected');
        }

        // 第一輪高亮梅花3
        if (this.firstRound && card.suit === '♣' && card.rank === '3') {
            cardDiv.classList.add('club-3-highlight');
            cardDiv.style.border = '3px solid #ffd700';
            cardDiv.style.boxShadow = '0 0 10px #ffd700';
            cardDiv.style.animation = 'pulse 2s infinite';
        }

        return cardDiv;
    }

    toggleCardSelection(index) {
        if (this.gameState !== GameState.PLAYING || this.currentPlayerIndex !== 0) {
            return;
        }
        
        const cardIndex = this.selectedCards.indexOf(index);
        if (cardIndex > -1) {
            this.selectedCards.splice(cardIndex, 1);
        } else {
            this.selectedCards.push(index);
        }
        
        this.updateUI();
    }

    updateGameInfo() {
        this.currentPlayerSpan.textContent = this.players[this.currentPlayerIndex]?.name || '';
        
        if (this.gameState === GameState.PLAYING) {
            this.gameStatus.textContent = '遊戲進行中';
        } else if (this.gameState === GameState.ENDED) {
            this.gameStatus.textContent = '遊戲結束';
        } else {
            this.gameStatus.textContent = '遊戲準備中...';
        }
    }

    updateControls() {
        const isHumanTurn = this.currentPlayerIndex === 0 && this.gameState === GameState.PLAYING;
        
        this.playButton.disabled = !isHumanTurn || this.selectedCards.length === 0;
        this.passButton.disabled = !isHumanTurn || (this.firstRound && this.hasClub3());
    }

    hasClub3() {
        const player = this.players[0];
        return player.cards.some(card => card.suit === '♣' && card.rank === '3');
    }

    updatePlayedCards() {
        if (!this.lastPlay) {
            this.playedCardsDiv.innerHTML = '<p style="color: #ccc;">尚未出牌</p>';
            this.lastPlayInfo.textContent = '';
        } else {
            this.playedCardsDiv.innerHTML = '';
            this.lastPlay.cards.forEach(card => {
                const cardDiv = document.createElement('div');
                cardDiv.className = `card ${card.isRed() ? 'red' : 'black'}`;
                cardDiv.innerHTML = `
                    <span class="card-rank">${card.rank}</span>
                    <span class="card-suit">${card.suit}</span>
                `;
                this.playedCardsDiv.appendChild(cardDiv);
            });
            
            this.lastPlayInfo.textContent = `${this.players[this.lastPlayerIndex].name} 出了 ${this.getHandTypeName(this.lastPlay.type)}`;
        }
    }

    updatePlayerCounts() {
        for (let i = 0; i < this.players.length; i++) {
            const countElement = document.getElementById(`player${i + 1}Count`);
            if (countElement && i !== 0) {
                countElement.textContent = this.players[i].cards.length;
            }
        }
    }

    playCards() {
        if (this.selectedCards.length === 0) return;
        
        const player = this.players[0];
        const cards = this.selectedCards.map(index => player.cards[index]);
        
        // 檢查第一輪必須包含梅花3
        if (this.firstRound && !cards.some(card => card.suit === '♣' && card.rank === '3')) {
            this.showMessage('第一輪必須出梅花3！');
            return;
        }
        
        const hand = this.identifyHand(cards);
        
        if (!hand) {
            this.showMessage('無效的牌組合！');
            return;
        }
        
        if (!this.isValidPlay(hand)) {
            if (this.firstRound) {
                this.showMessage('第一輪出牌無效！必須出包含梅花3(♣3)的牌組（可以是單張、對子、三條等任何牌型，只要包含梅花3）。');
            } else {
                this.showMessage('出牌無效！必須大過上一手牌。');
            }
            return;
        }
        
        // 出牌成功
        this.makePlay(0, cards, hand);
    }

    makePlay(playerIndex, cards, hand) {
        const player = this.players[playerIndex];
        
        // 從手牌中移除出的牌
        cards.forEach(card => {
            const index = player.cards.findIndex(c => c.value === card.value);
            if (index > -1) {
                player.cards.splice(index, 1);
            }
        });
        
        this.lastPlay = hand;
        this.lastPlayerIndex = playerIndex;
        this.passCount = 0;
        this.selectedCards = [];
        this.firstRound = false;
        
        // 檢查是否獲勝
        if (player.cards.length === 0) {
            this.endGame(playerIndex);
            return;
        }
        
        this.nextTurn();
    }

    pass() {
        if (this.firstRound && this.hasClub3()) {
            this.showMessage('第一輪不能Pass！');
            return;
        }
        
        this.passCount++;
        this.showMessage(`${this.players[this.currentPlayerIndex].name} Pass`);
        
        if (this.passCount === 3) {
            // 其他三家都Pass，清空桌面
            this.lastPlay = null;
            this.lastPlayerIndex = -1;
            this.passCount = 0;
            this.showMessage('所有人都Pass，清空桌面！');
        }
        
        this.nextTurn();
    }

    nextTurn() {
        this.currentPlayerIndex = (this.currentPlayerIndex + 1) % 4;
        
        // 如果回到上一個出牌的玩家，清空桌面
        if (this.currentPlayerIndex === this.lastPlayerIndex) {
            this.lastPlay = null;
            this.lastPlayerIndex = -1;
            this.passCount = 0;
        }
        
        this.updateUI();
        
        // 如果是電腦玩家，自動出牌
        if (this.currentPlayerIndex !== 0 && this.gameState === GameState.PLAYING) {
            const player = this.players[this.currentPlayerIndex];
            const thinkingTime = this.calculateThinkingTime(player);

            this.showMessage(`${player.name} 正在思考中...`);
            setTimeout(() => this.computerPlay(), thinkingTime);
        }
    }

    computerPlay() {
        const player = this.players[this.currentPlayerIndex];
        console.log(`AI玩家 ${player.name} 開始出牌，第一輪：${this.firstRound}`);

        // 使用增強的AI策略
        const aiStrategy = new AIStrategy(player, this);
        const decision = aiStrategy.makeDecision();

        console.log(`AI決策：${decision.action}`, decision.hand ? `牌型：${decision.hand.type}` : '');

        if (decision.action === 'pass') {
            // 檢查是否可以Pass
            if (!(this.firstRound && player.cards.some(card => card.suit === '♣' && card.rank === '3'))) {
                this.pass();
                return;
            } else {
                console.error('第一輪AI不能Pass，但AI嘗試Pass！');
                // 強制出單張梅花3
                const club3 = player.cards.find(card => card.suit === '♣' && card.rank === '3');
                if (club3) {
                    const singleClub3 = this.identifyHand([club3]);
                    if (singleClub3) {
                        this.makePlay(this.currentPlayerIndex, [club3], singleClub3);
                        this.showMessage(`${player.name} 被迫出單張梅花3`);
                        return;
                    }
                }
            }
        }

        if (decision.action === 'play' && decision.hand) {
            this.makePlay(this.currentPlayerIndex, decision.hand.cards, decision.hand);
            const message = this.generateAIMessage(player, decision.hand);
            this.showMessage(message);
        } else {
            console.error('AI決策無效！', decision);
            // 緊急備用方案：如果是第一輪且AI有梅花3，強制出單張梅花3
            if (this.firstRound) {
                const club3 = player.cards.find(card => card.suit === '♣' && card.rank === '3');
                if (club3) {
                    console.log('使用緊急備用方案：強制出單張梅花3');
                    const singleClub3 = this.identifyHand([club3]);
                    if (singleClub3) {
                        this.makePlay(this.currentPlayerIndex, [club3], singleClub3);
                        this.showMessage(`${player.name} 出了單張梅花3`);
                        return;
                    }
                }
            }
            // 如果還是不行，就Pass（雖然第一輪不應該Pass）
            this.pass();
        }
    }

    findValidHands(cards) {
        const validHands = [];
        
        // 單張
        if (!this.lastPlay || this.lastPlay.type === HandType.SINGLE) {
            cards.forEach(card => {
                const hand = this.identifyHand([card]);
                if (hand && this.isValidPlay(hand)) {
                    validHands.push(hand);
                }
            });
        }
        
        // 對子
        if (!this.lastPlay || this.lastPlay.type === HandType.PAIR) {
            for (let i = 0; i < cards.length - 1; i++) {
                for (let j = i + 1; j < cards.length; j++) {
                    if (cards[i].rank === cards[j].rank) {
                        const hand = this.identifyHand([cards[i], cards[j]]);
                        if (hand && this.isValidPlay(hand)) {
                            validHands.push(hand);
                        }
                    }
                }
            }
        }

        // 三條
        if (!this.lastPlay || this.lastPlay.type === HandType.THREE) {
            this.findThreeOfAKind(cards, validHands);
        }

        // 順子
        if (!this.lastPlay || this.lastPlay.type === HandType.STRAIGHT) {
            this.findStraights(cards, validHands);
        }

        // 葫蘆
        if (!this.lastPlay || this.lastPlay.type === HandType.FULL_HOUSE) {
            this.findFullHouses(cards, validHands);
        }

        // 鐵支
        if (!this.lastPlay || this.lastPlay.type === HandType.FOUR_OF_KIND) {
            this.findFourOfAKind(cards, validHands);
        }

        // 同花順
        if (!this.lastPlay || this.lastPlay.type === HandType.STRAIGHT_FLUSH) {
            this.findStraightFlushes(cards, validHands);
        }

        return validHands;
    }

    identifyHand(cards) {
        if (cards.length === 0) return null;
        
        cards.sort((a, b) => a.value - b.value);
        
        switch (cards.length) {
            case 1:
                return { type: HandType.SINGLE, cards, value: cards[0].value };
            
            case 2:
                if (cards[0].rank === cards[1].rank) {
                    return { type: HandType.PAIR, cards, value: Math.max(cards[0].value, cards[1].value) };
                }
                break;
            
            case 3:
                if (cards[0].rank === cards[1].rank && cards[1].rank === cards[2].rank) {
                    return { type: HandType.THREE, cards, value: cards[2].value };
                }
                break;
            
            case 5:
                // 檢查順子
                if (this.isStraight(cards)) {
                    return { type: HandType.STRAIGHT, cards, value: cards[4].value };
                }
                // 檢查葫蘆
                if (this.isFullHouse(cards)) {
                    return { type: HandType.FULL_HOUSE, cards, value: this.getFullHouseValue(cards) };
                }
                // 檢查同花順
                if (this.isStraightFlush(cards)) {
                    return { type: HandType.STRAIGHT_FLUSH, cards, value: cards[4].value * 100 };
                }
                break;
        }
        
        return null;
    }

    isStraight(cards) {
        if (cards.length !== 5) return false;
        
        for (let i = 0; i < cards.length - 1; i++) {
            if (RANKS[cards[i + 1].rank] - RANKS[cards[i].rank] !== 1) {
                return false;
            }
        }
        return true;
    }

    isFullHouse(cards) {
        if (cards.length !== 5) return false;
        
        const ranks = {};
        cards.forEach(card => {
            ranks[card.rank] = (ranks[card.rank] || 0) + 1;
        });
        
        const counts = Object.values(ranks);
        return counts.includes(3) && counts.includes(2);
    }

    isStraightFlush(cards) {
        if (cards.length !== 5) return false;
        
        const suit = cards[0].suit;
        return cards.every(card => card.suit === suit) && this.isStraight(cards);
    }

    getFullHouseValue(cards) {
        const ranks = {};
        cards.forEach(card => {
            ranks[card.rank] = (ranks[card.rank] || 0) + 1;
        });
        
        for (const [rank, count] of Object.entries(ranks)) {
            if (count === 3) {
                return RANKS[rank] * 100;
            }
        }
        return 0;
    }

    isValidPlay(hand) {
        // 第一輪必須包含梅花3
        if (this.firstRound) {
            return hand.cards.some(card => card.suit === '♣' && card.rank === '3');
        }
        
        // 如果沒有上一手牌，任何牌都可以出
        if (!this.lastPlay) {
            return true;
        }
        
        // 必須是相同類型
        if (hand.type !== this.lastPlay.type) {
            return false;
        }
        
        // 比較大小
        return hand.value > this.lastPlay.value;
    }

    getHandTypeName(type) {
        const names = {
            [HandType.SINGLE]: '單張',
            [HandType.PAIR]: '對子',
            [HandType.THREE]: '三條',
            [HandType.STRAIGHT]: '順子',
            [HandType.FULL_HOUSE]: '葫蘆',
            [HandType.FOUR_OF_KIND]: '鐵支',
            [HandType.STRAIGHT_FLUSH]: '同花順'
        };
        return names[type] || '未知';
    }

    endGame(winnerIndex) {
        this.gameState = GameState.ENDED;
        const winner = this.players[winnerIndex];
        this.showMessage(`🎉 ${winner.name} 獲勝！`);
        this.gameStatus.textContent = `${winner.name} 獲勝！`;

        // 更新統計
        this.updateStats(winnerIndex);

        this.updateUI();
    }

    updateStats(winnerIndex) {
        this.gameStats.totalGames++;

        if (winnerIndex === 0) {
            this.gameStats.playerWins++;
        } else {
            this.gameStats.aiWins++;
        }

        this.saveStats();
        this.updateStatsDisplay();
    }

    loadStats() {
        const savedStats = localStorage.getItem('bigTwoStats');
        if (savedStats) {
            this.gameStats = JSON.parse(savedStats);
        }
        this.updateStatsDisplay();
    }

    saveStats() {
        localStorage.setItem('bigTwoStats', JSON.stringify(this.gameStats));
    }

    updateStatsDisplay() {
        const totalGamesElement = document.getElementById('totalGames');
        const aiWinRateElement = document.getElementById('aiWinRate');
        const playerWinRateElement = document.getElementById('playerWinRate');

        if (totalGamesElement) {
            totalGamesElement.textContent = this.gameStats.totalGames;
        }

        if (this.gameStats.totalGames > 0) {
            const aiWinRate = Math.round((this.gameStats.aiWins / this.gameStats.totalGames) * 100);
            const playerWinRate = Math.round((this.gameStats.playerWins / this.gameStats.totalGames) * 100);

            if (aiWinRateElement) {
                aiWinRateElement.textContent = `${aiWinRate}%`;
            }
            if (playerWinRateElement) {
                playerWinRateElement.textContent = `${playerWinRate}%`;
            }
        } else {
            if (aiWinRateElement) aiWinRateElement.textContent = '0%';
            if (playerWinRateElement) playerWinRateElement.textContent = '0%';
        }
    }

    showMessage(message) {
        this.messageArea.textContent = message;
        setTimeout(() => {
            this.messageArea.textContent = '';
        }, 3000);
    }

    calculateThinkingTime(player) {
        // 根據AI難度和個性計算思考時間
        let baseTime = 1000; // 基礎時間1秒

        switch (player.aiLevel) {
            case AILevel.EASY:
                baseTime = 800;
                break;
            case AILevel.MEDIUM:
                baseTime = 1200;
                break;
            case AILevel.HARD:
                baseTime = 1800;
                break;
        }

        // 根據個性調整
        const personalityFactor = player.personality.patience * 0.5 + 0.5;
        baseTime *= personalityFactor;

        // 添加隨機變化
        const randomFactor = 0.7 + Math.random() * 0.6; // 0.7-1.3

        return Math.floor(baseTime * randomFactor);
    }

    generateAIMessage(player, hand) {
        const handTypeName = this.getHandTypeName(hand.type);
        const cardCount = player.cards.length;

        // 根據AI難度和個性生成不同的消息
        const messages = {
            [AILevel.EASY]: [
                `${player.name} 出了 ${handTypeName}`,
                `${player.name}: 我出 ${handTypeName}！`,
                `${player.name} 打出 ${handTypeName}`
            ],
            [AILevel.MEDIUM]: [
                `${player.name} 深思熟慮後出了 ${handTypeName}`,
                `${player.name}: 這手 ${handTypeName} 應該不錯`,
                `${player.name} 策略性地打出 ${handTypeName}`
            ],
            [AILevel.HARD]: [
                `${player.name} 精心計算後出了 ${handTypeName}`,
                `${player.name}: 完美的 ${handTypeName}！`,
                `${player.name} 展現高超技巧，打出 ${handTypeName}`
            ]
        };

        // 特殊情況的消息
        if (cardCount <= 3) {
            return `${player.name}: 只剩 ${cardCount} 張牌了！出 ${handTypeName}`;
        }

        if (hand.type === HandType.STRAIGHT_FLUSH) {
            return `${player.name}: 同花順！這是我的王牌！`;
        }

        if (hand.type === HandType.FOUR_OF_KIND) {
            return `${player.name}: 鐵支出場！誰能擋得住？`;
        }

        const levelMessages = messages[player.aiLevel] || messages[AILevel.MEDIUM];
        return levelMessages[Math.floor(Math.random() * levelMessages.length)];
    }

    // 輔助方法：尋找三條
    findThreeOfAKind(cards, validHands) {
        const rankGroups = this.groupCardsByRank(cards);

        for (const [rank, rankCards] of Object.entries(rankGroups)) {
            if (rankCards.length >= 3) {
                // 選擇最好的三張
                const sortedCards = rankCards.sort((a, b) => b.value - a.value);
                const threeCards = sortedCards.slice(0, 3);
                const hand = this.identifyHand(threeCards);
                if (hand && this.isValidPlay(hand)) {
                    validHands.push(hand);
                }
            }
        }
    }

    // 輔助方法：尋找順子
    findStraights(cards, validHands) {
        if (cards.length < 5) return;

        const sortedCards = [...cards].sort((a, b) => RANKS[a.rank] - RANKS[b.rank]);

        for (let i = 0; i <= sortedCards.length - 5; i++) {
            for (let j = i + 4; j < sortedCards.length; j++) {
                const straightCards = this.findStraightCombination(sortedCards, i, j);
                if (straightCards && straightCards.length === 5) {
                    const hand = this.identifyHand(straightCards);
                    if (hand && this.isValidPlay(hand)) {
                        validHands.push(hand);
                    }
                }
            }
        }
    }

    // 輔助方法：尋找葫蘆
    findFullHouses(cards, validHands) {
        if (cards.length < 5) return;

        const rankGroups = this.groupCardsByRank(cards);
        const threeOfAKindRanks = [];
        const pairRanks = [];

        for (const [rank, rankCards] of Object.entries(rankGroups)) {
            if (rankCards.length >= 3) {
                threeOfAKindRanks.push({ rank, cards: rankCards });
            }
            if (rankCards.length >= 2) {
                pairRanks.push({ rank, cards: rankCards });
            }
        }

        // 組合三條和對子
        for (const threeGroup of threeOfAKindRanks) {
            for (const pairGroup of pairRanks) {
                if (threeGroup.rank !== pairGroup.rank) {
                    const threeCards = threeGroup.cards.slice(0, 3);
                    const pairCards = pairGroup.cards.slice(0, 2);
                    const fullHouseCards = [...threeCards, ...pairCards];

                    const hand = this.identifyHand(fullHouseCards);
                    if (hand && this.isValidPlay(hand)) {
                        validHands.push(hand);
                    }
                }
            }
        }
    }

    // 輔助方法：尋找鐵支
    findFourOfAKind(cards, validHands) {
        const rankGroups = this.groupCardsByRank(cards);

        for (const [rank, rankCards] of Object.entries(rankGroups)) {
            if (rankCards.length >= 4) {
                const fourCards = rankCards.slice(0, 4);
                // 鐵支需要加一張單牌
                for (const singleCard of cards) {
                    if (singleCard.rank !== rank) {
                        const hand = this.identifyHand([...fourCards, singleCard]);
                        if (hand && this.isValidPlay(hand)) {
                            validHands.push(hand);
                        }
                    }
                }
            }
        }
    }

    // 輔助方法：尋找同花順
    findStraightFlushes(cards, validHands) {
        if (cards.length < 5) return;

        const suitGroups = this.groupCardsBySuit(cards);

        for (const [suit, suitCards] of Object.entries(suitGroups)) {
            if (suitCards.length >= 5) {
                this.findStraights(suitCards, validHands);
            }
        }
    }

    // 輔助方法：按花色分組
    groupCardsBySuit(cards) {
        const groups = {};
        cards.forEach(card => {
            if (!groups[card.suit]) {
                groups[card.suit] = [];
            }
            groups[card.suit].push(card);
        });
        return groups;
    }

    // 輔助方法：按點數分組
    groupCardsByRank(cards) {
        const groups = {};
        cards.forEach(card => {
            if (!groups[card.rank]) {
                groups[card.rank] = [];
            }
            groups[card.rank].push(card);
        });
        return groups;
    }

    // 輔助方法：尋找順子組合
    findStraightCombination(sortedCards, startIndex, endIndex) {
        const straightCards = [];
        let currentRank = RANKS[sortedCards[startIndex].rank];

        for (let i = startIndex; i <= endIndex; i++) {
            const cardRank = RANKS[sortedCards[i].rank];
            if (cardRank === currentRank) {
                straightCards.push(sortedCards[i]);
                currentRank++;
            } else if (cardRank > currentRank) {
                return null; // 不是連續的
            }
        }

        return straightCards.length === 5 ? straightCards : null;
    }
}

// 增強的AI策略類別
class AIStrategy {
    constructor(player, game) {
        this.player = player;
        this.game = game;
        this.validHands = this.game.findValidHands(player.cards);

        // 調試信息
        if (game.firstRound) {
            console.log(`AI ${player.name} 第一輪，找到 ${this.validHands.length} 個有效手牌`);
            const hasClub3 = player.cards.some(card => card.suit === '♣' && card.rank === '3');
            console.log(`AI ${player.name} 擁有梅花3：${hasClub3}`);
        }
    }

    makeDecision() {
        // 第一輪必須出梅花3
        if (this.game.firstRound && this.player.cards.some(card => card.suit === '♣' && card.rank === '3')) {
            return this.playFirstRound();
        }

        // 根據AI難度選擇策略
        switch (this.player.aiLevel) {
            case AILevel.EASY:
                return this.easyStrategy();
            case AILevel.MEDIUM:
                return this.mediumStrategy();
            case AILevel.HARD:
                return this.hardStrategy();
            default:
                return this.mediumStrategy();
        }
    }

    playFirstRound() {
        // 找到包含梅花3的最小組合
        const club3 = this.player.cards.find(card => card.suit === '♣' && card.rank === '3');

        if (!club3) {
            console.error('AI玩家沒有梅花3，但被要求出第一輪！');
            return { action: 'pass' };
        }

        const validWithClub3 = this.validHands.filter(hand =>
            hand.cards.some(card => card.suit === '♣' && card.rank === '3')
        );

        if (validWithClub3.length > 0) {
            // 選擇最小的包含梅花3的組合
            console.log(`AI找到${validWithClub3.length}個包含梅花3的有效組合`);
            return { action: 'play', hand: validWithClub3[0] };
        }

        // 如果沒有組合，就出單張梅花3
        console.log('AI出單張梅花3');
        const singleClub3 = this.game.identifyHand([club3]);
        if (!singleClub3) {
            console.error('無法識別單張梅花3');
            return { action: 'pass' };
        }
        return { action: 'play', hand: singleClub3 };
    }

    easyStrategy() {
        // 簡單策略：隨機選擇或出最小的牌
        if (this.validHands.length === 0) {
            return { action: 'pass' };
        }

        // 30% 機率Pass（如果不是第一輪）
        if (this.game.lastPlay && Math.random() < 0.3) {
            return { action: 'pass' };
        }

        // 出最小的牌
        return { action: 'play', hand: this.validHands[0] };
    }

    mediumStrategy() {
        if (this.validHands.length === 0) {
            return { action: 'pass' };
        }

        // 考慮手牌數量和牌型價值
        const opponentThreat = this.assessOpponentThreat();

        // 如果手牌很少，更積極出牌
        if (this.player.cards.length <= 3) {
            return { action: 'play', hand: this.selectBestHand() };
        }

        // 如果對手威脅很大，考慮阻擋
        if (opponentThreat > 0.7) {
            const blockingHand = this.findBlockingHand();
            if (blockingHand) {
                return { action: 'play', hand: blockingHand };
            }
        }

        // 根據個性決定
        const passChance = this.calculatePassChance();
        if (Math.random() < passChance) {
            return { action: 'pass' };
        }

        return { action: 'play', hand: this.selectOptimalHand() };
    }

    hardStrategy() {
        if (this.validHands.length === 0) {
            return { action: 'pass' };
        }

        // 高級策略：考慮多種因素
        const gameState = this.analyzeGameState();
        const bestMove = this.calculateBestMove(gameState);

        return bestMove;
    }

    evaluateHand() {
        // 評估手牌價值
        let value = 0;
        const cardCounts = this.getCardCounts();

        // 計算牌型價值
        Object.values(cardCounts).forEach(count => {
            if (count >= 2) value += count * 10;
        });

        // 高牌價值
        this.player.cards.forEach(card => {
            if (RANKS[card.rank] >= 13) value += 5; // K, A, 2
        });

        return value / this.player.cards.length;
    }

    assessOpponentThreat() {
        // 評估對手威脅程度
        let threat = 0;

        this.game.players.forEach((player, index) => {
            if (index !== this.game.currentPlayerIndex) {
                const cardCount = player.cards.length;
                if (cardCount <= 3) threat += 0.3;
                if (cardCount <= 1) threat += 0.5;
            }
        });

        return Math.min(threat, 1.0);
    }

    calculatePassChance() {
        let passChance = 0.2; // 基礎Pass機率

        // 根據個性調整
        passChance += this.player.personality.patience * 0.3;
        passChance -= this.player.personality.aggressiveness * 0.2;

        // 根據手牌情況調整
        if (this.player.cards.length > 10) passChance += 0.1;
        if (this.player.cards.length < 5) passChance -= 0.2;

        return Math.max(0, Math.min(passChance, 0.6));
    }

    selectBestHand() {
        // 選擇最佳手牌組合
        if (this.validHands.length === 0) return null;

        // 優先選擇能清空手牌的組合
        if (this.player.cards.length <= 5) {
            const finishingHand = this.validHands.find(hand =>
                hand.cards.length === this.player.cards.length
            );
            if (finishingHand) return finishingHand;
        }

        // 選擇價值最低的有效組合
        return this.validHands.reduce((best, current) =>
            current.value < best.value ? current : best
        );
    }

    selectOptimalHand() {
        // 選擇最優手牌組合
        let bestHand = this.validHands[0];
        let bestScore = this.evaluateHandScore(bestHand);

        for (let i = 1; i < this.validHands.length; i++) {
            const score = this.evaluateHandScore(this.validHands[i]);
            if (score > bestScore) {
                bestScore = score;
                bestHand = this.validHands[i];
            }
        }

        return bestHand;
    }

    evaluateHandScore(hand) {
        let score = 0;

        // 牌型價值
        const typeValues = {
            [HandType.SINGLE]: 1,
            [HandType.PAIR]: 3,
            [HandType.THREE]: 5,
            [HandType.STRAIGHT]: 8,
            [HandType.FULL_HOUSE]: 12,
            [HandType.FOUR_OF_KIND]: 15,
            [HandType.STRAIGHT_FLUSH]: 20
        };
        score += typeValues[hand.type] || 0;

        // 考慮剩餘手牌
        const remainingCards = this.player.cards.length - hand.cards.length;
        if (remainingCards === 0) score += 50; // 能獲勝
        if (remainingCards <= 2) score += 20; // 接近獲勝

        // 牌的相對價值（較小的牌得分更高）
        const avgValue = hand.cards.reduce((sum, card) => sum + RANKS[card.rank], 0) / hand.cards.length;
        score += (20 - avgValue) * 2;

        return score;
    }

    findBlockingHand() {
        // 找到能阻擋對手的手牌
        if (!this.game.lastPlay) return null;

        // 選擇剛好大過對手的最小組合
        const blockingHands = this.validHands.filter(hand =>
            hand.type === this.game.lastPlay.type &&
            hand.value > this.game.lastPlay.value
        );

        if (blockingHands.length === 0) return null;

        return blockingHands.reduce((best, current) =>
            current.value < best.value ? current : best
        );
    }

    analyzeGameState() {
        return {
            myCardCount: this.player.cards.length,
            opponentCardCounts: this.game.players.map(p => p.cards.length),
            lastPlay: this.game.lastPlay,
            passCount: this.game.passCount,
            isEndGame: this.game.players.some(p => p.cards.length <= 3)
        };
    }

    calculateBestMove(gameState) {
        // 複雜的決策算法
        if (gameState.isEndGame) {
            return this.endGameStrategy(gameState);
        }

        // 正常遊戲策略
        const moveScores = this.validHands.map(hand => ({
            hand,
            score: this.calculateMoveScore(hand, gameState)
        }));

        // 考慮Pass的分數
        const passScore = this.calculatePassScore(gameState);
        moveScores.push({ hand: null, score: passScore });

        // 選擇最高分的行動
        const bestMove = moveScores.reduce((best, current) =>
            current.score > best.score ? current : best
        );

        return bestMove.hand ?
            { action: 'play', hand: bestMove.hand } :
            { action: 'pass' };
    }

    endGameStrategy(gameState) {
        // 終局策略：更積極
        if (this.validHands.length === 0) {
            return { action: 'pass' };
        }

        // 如果能獲勝，立即出牌
        const winningHand = this.validHands.find(hand =>
            hand.cards.length === this.player.cards.length
        );
        if (winningHand) {
            return { action: 'play', hand: winningHand };
        }

        // 在終局階段，考慮阻擋其他接近獲勝的玩家
        const nearWinPlayers = gameState.opponentCardCounts.filter(count => count <= 2);
        if (nearWinPlayers.length > 0) {
            // 更積極地出牌阻擋
            const blockingHand = this.findBlockingHand();
            if (blockingHand) {
                return { action: 'play', hand: blockingHand };
            }
        }

        // 選擇最大化剩餘手牌價值的組合
        return { action: 'play', hand: this.selectBestHand() };
    }

    calculateMoveScore(hand, gameState) {
        let score = this.evaluateHandScore(hand);

        // 終局加分
        if (gameState.isEndGame) {
            score *= 1.5;
        }

        // 阻擋對手加分
        if (this.isBlockingMove(hand)) {
            score += 10;
        }

        return score;
    }

    calculatePassScore(gameState) {
        let score = 5; // 基礎Pass分數

        // 根據個性調整
        score += this.player.personality.patience * 10;

        // 如果對手快要獲勝，Pass分數降低
        if (gameState.isEndGame) {
            score -= 15;
        }

        return score;
    }

    isBlockingMove(hand) {
        if (!this.game.lastPlay) return false;
        return hand.type === this.game.lastPlay.type &&
               hand.value > this.game.lastPlay.value;
    }

    getCardCounts() {
        const counts = {};
        this.player.cards.forEach(card => {
            counts[card.rank] = (counts[card.rank] || 0) + 1;
        });
        return counts;
    }
}

// 初始化遊戲
const game = new BigTwo();