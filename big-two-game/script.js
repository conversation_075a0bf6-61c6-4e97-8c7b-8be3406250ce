        const player = this.players[0];
        this.playerCardsDiv.innerHTML = '';
        
        player.cards.forEach((card, index) => {
            const cardElement = this.createCardElement(card, index);
            this.playerCardsDiv.appendChild(cardElement);
        });
    }

    createCardElement(card, index) {
        const cardDiv = document.createElement('div');
        cardDiv.className = `card ${card.isRed() ? 'red' : 'black'}`;
        cardDiv.dataset.index = index;
        
        const rankSpan = document.createElement('span');
        rankSpan.className = 'card-rank';
        rankSpan.textContent = card.rank;
        
        const suitSpan = document.createElement('span');
        suitSpan.className = 'card-suit';
        suitSpan.textContent = card.suit;
        
        cardDiv.appendChild(rankSpan);
        cardDiv.appendChild(suitSpan);
        
        cardDiv.addEventListener('click', () => this.toggleCardSelection(index));
        
        if (this.selectedCards.includes(index)) {
            cardDiv.classList.add('selected');
        }
        
        return cardDiv;
    }

    toggleCardSelection(index) {
        if (this.gameState !== GameState.PLAYING || this.currentPlayerIndex !== 0) {
            return;
        }
        
        const cardIndex = this.selectedCards.indexOf(index);
        if (cardIndex > -1) {
            this.selectedCards.splice(cardIndex, 1);
        } else {
            this.selectedCards.push(index);
        }
        
        this.updateUI();
    }

    updateGameInfo() {
        this.currentPlayerSpan.textContent = this.players[this.currentPlayerIndex]?.name || '';
        
        if (this.gameState === GameState.PLAYING) {
            this.gameStatus.textContent = '遊戲進行中';
        } else if (this.gameState === GameState.ENDED) {
            this.gameStatus.textContent = '遊戲結束';
        } else {
            this.gameStatus.textContent = '遊戲準備中...';
        }
    }

    updateControls() {
        const isHumanTurn = this.currentPlayerIndex === 0 && this.gameState === GameState.PLAYING;
        
        this.playButton.disabled = !isHumanTurn || this.selectedCards.length === 0;
        this.passButton.disabled = !isHumanTurn || (this.firstRound && this.hasClub3());
    }

    hasClub3() {
        const player = this.players[0];
        return player.cards.some(card => card.suit === '♣' && card.rank === '3');
    }

    updatePlayedCards() {
        if (!this.lastPlay) {
            this.playedCardsDiv.innerHTML = '<p style="color: #ccc;">尚未出牌</p>';
            this.lastPlayInfo.textContent = '';
        } else {
            this.playedCardsDiv.innerHTML = '';
            this.lastPlay.cards.forEach(card => {
                const cardDiv = document.createElement('div');
                cardDiv.className = `card ${card.isRed() ? 'red' : 'black'}`;
                cardDiv.innerHTML = `
                    <span class="card-rank">${card.rank}</span>
                    <span class="card-suit">${card.suit}</span>
                `;
                this.playedCardsDiv.appendChild(cardDiv);
            });
            
            this.lastPlayInfo.textContent = `${this.players[this.lastPlayerIndex].name} 出了 ${this.getHandTypeName(this.lastPlay.type)}`;
        }
    }

    updatePlayerCounts() {
        for (let i = 0; i < this.players.length; i++) {
            const countElement = document.getElementById(`player${i + 1}Count`);
            if (countElement && i !== 0) {
                countElement.textContent = this.players[i].cards.length;
            }
        }
    }

    playCards() {
        if (this.selectedCards.length === 0) return;
        
        const player = this.players[0];
        const cards = this.selectedCards.map(index => player.cards[index]);
        
        // 檢查第一輪必須包含梅花3
        if (this.firstRound && !cards.some(card => card.suit === '♣' && card.rank === '3')) {
            this.showMessage('第一輪必須出梅花3！');
            return;
        }
        
        const hand = this.identifyHand(cards);
        
        if (!hand) {
            this.showMessage('無效的牌組合！');
            return;
        }
        
        if (!this.isValidPlay(hand)) {
            this.showMessage('出牌無效！必須大過上一手牌。');
            return;
        }
        
        // 出牌成功
        this.makePlay(0, cards, hand);
    }

    makePlay(playerIndex, cards, hand) {
        const player = this.players[playerIndex];
        
        // 從手牌中移除出的牌
        cards.forEach(card => {
            const index = player.cards.findIndex(c => c.value === card.value);
            if (index > -1) {
                player.cards.splice(index, 1);
            }
        });
        
        this.lastPlay = hand;
        this.lastPlayerIndex = playerIndex;
        this.passCount = 0;
        this.selectedCards = [];
        this.firstRound = false;
        
        // 檢查是否獲勝
        if (player.cards.length === 0) {
            this.endGame(playerIndex);
            return;
        }
        
        this.nextTurn();
    }

    pass() {
        if (this.firstRound && this.hasClub3()) {
            this.showMessage('第一輪不能Pass！');
            return;
        }
        
        this.passCount++;
        this.showMessage(`${this.players[this.currentPlayerIndex].name} Pass`);
        
        if (this.passCount === 3) {
            // 其他三家都Pass，清空桌面
            this.lastPlay = null;
            this.lastPlayerIndex = -1;
            this.passCount = 0;
            this.showMessage('所有人都Pass，清空桌面！');
        }
        
        this.nextTurn();
    }

    nextTurn() {
        this.currentPlayerIndex = (this.currentPlayerIndex + 1) % 4;
        
        // 如果回到上一個出牌的玩家，清空桌面
        if (this.currentPlayerIndex === this.lastPlayerIndex) {
            this.lastPlay = null;
            this.lastPlayerIndex = -1;
            this.passCount = 0;
        }
        
        this.updateUI();
        
        // 如果是電腦玩家，自動出牌
        if (this.currentPlayerIndex !== 0 && this.gameState === GameState.PLAYING) {
            setTimeout(() => this.computerPlay(), 1500);
        }
    }

    computerPlay() {
        const player = this.players[this.currentPlayerIndex];
        
        // 簡單的AI策略
        let validHands = this.findValidHands(player.cards);
        
        if (validHands.length === 0 || (this.lastPlay && Math.random() > 0.7)) {
            // 沒有有效牌或隨機決定Pass
            if (!(this.firstRound && player.cards.some(card => card.suit === '♣' && card.rank === '3'))) {
                this.pass();
                return;
            }
        }
        
        // 選擇最小的有效牌組
        if (validHands.length > 0) {
            const hand = validHands[0];
            this.makePlay(this.currentPlayerIndex, hand.cards, hand);
            this.showMessage(`${player.name} 出了 ${this.getHandTypeName(hand.type)}`);
        }
    }

    findValidHands(cards) {
        const validHands = [];
        
        // 單張
        if (!this.lastPlay || this.lastPlay.type === HandType.SINGLE) {
            cards.forEach(card => {
                const hand = this.identifyHand([card]);
                if (hand && this.isValidPlay(hand)) {
                    validHands.push(hand);
                }
            });
        }
        
        // 對子
        if (!this.lastPlay || this.lastPlay.type === HandType.PAIR) {
            for (let i = 0; i < cards.length - 1; i++) {
                for (let j = i + 1; j < cards.length; j++) {
                    if (cards[i].rank === cards[j].rank) {
                        const hand = this.identifyHand([cards[i], cards[j]]);
                        if (hand && this.isValidPlay(hand)) {
                            validHands.push(hand);
                        }
                    }
                }
            }
        }
        
        // 這裡可以添加更多牌型的搜索邏輯
        
        return validHands;
    }

    identifyHand(cards) {
        if (cards.length === 0) return null;
        
        cards.sort((a, b) => a.value - b.value);
        
        switch (cards.length) {
            case 1:
                return { type: HandType.SINGLE, cards, value: cards[0].value };
            
            case 2:
                if (cards[0].rank === cards[1].rank) {
                    return { type: HandType.PAIR, cards, value: Math.max(cards[0].value, cards[1].value) };
                }
                break;
            
            case 3:
                if (cards[0].rank === cards[1].rank && cards[1].rank === cards[2].rank) {
                    return { type: HandType.THREE, cards, value: cards[2].value };
                }
                break;
            
            case 5:
                // 檢查順子
                if (this.isStraight(cards)) {
                    return { type: HandType.STRAIGHT, cards, value: cards[4].value };
                }
                // 檢查葫蘆
                if (this.isFullHouse(cards)) {
                    return { type: HandType.FULL_HOUSE, cards, value: this.getFullHouseValue(cards) };
                }
                // 檢查同花順
                if (this.isStraightFlush(cards)) {
                    return { type: HandType.STRAIGHT_FLUSH, cards, value: cards[4].value * 100 };
                }
                break;
        }
        
        return null;
    }

    isStraight(cards) {
        if (cards.length !== 5) return false;
        
        for (let i = 0; i < cards.length - 1; i++) {
            if (RANKS[cards[i + 1].rank] - RANKS[cards[i].rank] !== 1) {
                return false;
            }
        }
        return true;
    }

    isFullHouse(cards) {
        if (cards.length !== 5) return false;
        
        const ranks = {};
        cards.forEach(card => {
            ranks[card.rank] = (ranks[card.rank] || 0) + 1;
        });
        
        const counts = Object.values(ranks);
        return counts.includes(3) && counts.includes(2);
    }

    isStraightFlush(cards) {
        if (cards.length !== 5) return false;
        
        const suit = cards[0].suit;
        return cards.every(card => card.suit === suit) && this.isStraight(cards);
    }

    getFullHouseValue(cards) {
        const ranks = {};
        cards.forEach(card => {
            ranks[card.rank] = (ranks[card.rank] || 0) + 1;
        });
        
        for (const [rank, count] of Object.entries(ranks)) {
            if (count === 3) {
                return RANKS[rank] * 100;
            }
        }
        return 0;
    }

    isValidPlay(hand) {
        // 第一輪必須包含梅花3
        if (this.firstRound) {
            return hand.cards.some(card => card.suit === '♣' && card.rank === '3');
        }
        
        // 如果沒有上一手牌，任何牌都可以出
        if (!this.lastPlay) {
            return true;
        }
        
        // 必須是相同類型
        if (hand.type !== this.lastPlay.type) {
            return false;
        }
        
        // 比較大小
        return hand.value > this.lastPlay.value;
    }

    getHandTypeName(type) {
        const names = {
            [HandType.SINGLE]: '單張',
            [HandType.PAIR]: '對子',
            [HandType.THREE]: '三條',
            [HandType.STRAIGHT]: '順子',
            [HandType.FULL_HOUSE]: '葫蘆',
            [HandType.FOUR_OF_KIND]: '鐵支',
            [HandType.STRAIGHT_FLUSH]: '同花順'
        };
        return names[type] || '未知';
    }

    endGame(winnerIndex) {
        this.gameState = GameState.ENDED;
        const winner = this.players[winnerIndex];
        this.showMessage(`🎉 ${winner.name} 獲勝！`);
        this.gameStatus.textContent = `${winner.name} 獲勝！`;
        this.updateUI();
    }

    showMessage(message) {
        this.messageArea.textContent = message;
        setTimeout(() => {
            this.messageArea.textContent = '';
        }, 3000);
    }
}

// 初始化遊戲
const game = new BigTwo();